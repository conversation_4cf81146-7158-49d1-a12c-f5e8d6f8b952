# 3D体态仪云平台API接口索引

## 概述

本文档整理了3D体态仪设备与云平台交互的所有API接口，包括初始化流程、日常启动流程和日常检测流程中使用的接口。

## 统一API接口列表

### 设备管理类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `GET /api/v1/devices/{deviceId}/check` | 检查设备ID是否存在 | 验证设备是否已注册 | ✅ 推荐 |
| `POST /api/v1/devices` | 设备注册 | 注册新设备 | ✅ 推荐 |
| `PUT /api/v1/devices/{deviceId}` | 设备信息更新 | 更新设备信息 | ✅ 推荐 |
| `GET /api/v1/devices/{deviceId}/status` | 设备状态查询 | 查询设备当前状态和授权信息 | ✅ 推荐 |
| ~~`GET /api/device/status`~~ | ~~设备状态检查接口~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |

### 配置管理类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `GET /api/v1/devices/{deviceId}/config` | 获取设备配置 | 获取设备完整配置信息 | ✅ 推荐 |
| `PUT /api/v1/devices/{deviceId}/config` | 更新设备配置 | 更新设备配置信息 | ✅ 推荐 |
| `GET /api/v1/error-codes` | 获取错误代码配置 | 获取所有错误代码定义 | ✅ 推荐 |
| ~~`GET /api/device/config`~~ | ~~设备配置获取接口~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |

### 状态监控类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `POST /api/v1/devices/{deviceId}/heartbeat` | 设备心跳 | 设备定期发送状态信息 | ✅ 推荐 |
| `POST /api/v1/devices/{deviceId}/self-check` | 设备自检结果上报 | 上报设备自检结果 | ✅ 推荐 |
| `POST /api/v1/devices/{deviceId}/errors` | 错误日志上报 | 设备上报错误信息 | ✅ 推荐 |
| ~~`POST /api/heartbeat`~~ | ~~设备心跳接口~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/device/self-check`~~ | ~~设备自检结果上报接口~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |

### 用户管理类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `POST /api/v1/devices/{deviceId}/qrcode` | 生成设备二维码 | 根据设备ID生成二维码 | ✅ 推荐 |
| `GET /api/v1/devices/bind` | 获取绑定页面 | 通过二维码获取设备绑定页面 | ✅ 推荐 |
| `POST /api/v1/sms/verification-codes` | 发送验证码 | 向用户手机发送验证码 | ✅ 推荐 |
| `POST /api/v1/users/bind-device` | 用户设备绑定 | 验证码校验并绑定用户与设备 | ✅ 推荐 |
| `POST /api/v1/users/{userId}/profile` | 保存用户信息 | 保存用户个人基本信息 | ✅ 推荐 |
| ~~`POST /api/qrcode/generate`~~ | ~~生成二维码~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`GET /api/device/bind`~~ | ~~获取绑定页面~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/sms/send`~~ | ~~发送验证码~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/user/bind`~~ | ~~用户设备绑定~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/user/info`~~ | ~~保存用户信息~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |

### 检测数据类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `POST /api/v1/detections` | 创建检测记录 | 创建新的检测记录 | ✅ 推荐 |
| `POST /api/v1/detections/{detectionId}/metrics` | 上传体态检测数据 | 上传身高、体重、BMI等检测数据 | ✅ 推荐 |
| `POST /api/v1/detections/{detectionId}/photos` | 上传用户照片 | 上传用户检测照片 | ✅ 推荐 |
| `POST /api/v1/detections/{detectionId}/3d-models` | 上传3D模型数据 | 上传预处理后的3D模型数据 | ✅ 推荐 |
| `GET /api/v1/detections/{detectionId}/reports` | 生成用户报告 | 生成并获取用户体态检测报告 | ✅ 推荐 |
| ~~`POST /api/detection/metrics`~~ | ~~上传体态检测数据~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/detection/photos`~~ | ~~上传用户照片~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`POST /api/detection/3dmodel`~~ | ~~上传3D模型数据~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |
| ~~`GET /api/report/generate`~~ | ~~生成用户报告~~ | ~~已废弃，使用上述接口~~ | ❌ 废弃 |

### 系统管理类API

| API URL | API Title | 功能说明 | 状态 |
|---------|-----------|----------|------|
| `GET /api/v1/health` | 系统健康检查 | 检查云平台服务可用性 | ✅ 推荐 |
| `GET /api/v1/version` | API版本信息 | 获取当前API版本信息 | ✅ 推荐 |

## 基础信息

### 统一API设计规范

#### 1. 版本管理
- **API版本**: 所有API统一使用 `v1` 版本前缀
- **基础URL**: `https://api.cloudplatform.com/v1`
- **版本策略**:
  - 主版本号变更：不兼容的API修改
  - 次版本号变更：向后兼容的功能性新增
  - 修订版本号变更：向后兼容的问题修正

#### 2. URL设计原则
- **RESTful风格**: 使用标准HTTP方法（GET、POST、PUT、DELETE）
- **资源命名**: 使用复数名词（devices、users、detections）
- **层级关系**: 体现资源间的层级关系
  ```
  /api/v1/devices/{deviceId}/config
  /api/v1/users/{userId}/profile
  /api/v1/detections/{detectionId}/reports
  ```

#### 3. 认证与安全
- **认证方式**: API Key + Device ID
- **请求头**:
  ```
  Authorization: Bearer {api_key}
  X-Device-ID: {device_id}
  Content-Type: application/json
  ```
- **数据格式**: JSON
- **字符编码**: UTF-8
- **协议**: HTTPS

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "error": {
    "type": "validation_error",
    "details": "具体错误详情"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 统一业务流程

### 1. 设备初始化流程（统一版本）
设备首次启动或重置后的初始化过程：
1. 检查设备ID是否存在 (`GET /api/v1/devices/{deviceId}/check`)
2. 设备注册 (`POST /api/v1/devices`)
3. 获取设备配置 (`GET /api/v1/devices/{deviceId}/config`)
4. 系统健康检查 (`GET /api/v1/health`)
5. 获取错误代码配置 (`GET /api/v1/error-codes`)

### 2. 日常启动流程（统一版本）
设备每日启动时的标准流程：
1. 设备心跳 (`POST /api/v1/devices/{deviceId}/heartbeat`)
2. 获取设备配置 (`GET /api/v1/devices/{deviceId}/config`)
3. 设备状态检查 (`GET /api/v1/devices/{deviceId}/status`)
4. 设备自检结果上报 (`POST /api/v1/devices/{deviceId}/self-check`)

### 3. 日常检测流程（统一版本）
用户进行体态检测时的完整流程：
1. 生成设备二维码 (`POST /api/v1/devices/{deviceId}/qrcode`)
2. 用户扫码获取绑定页面 (`GET /api/v1/devices/bind`)
3. 发送验证码 (`POST /api/v1/sms/verification-codes`)
4. 用户设备绑定 (`POST /api/v1/users/bind-device`)
5. 保存用户信息 (`POST /api/v1/users/{userId}/profile`)
6. 创建检测记录 (`POST /api/v1/detections`)
7. 上传检测数据 (`POST /api/v1/detections/{detectionId}/metrics`)
8. 上传用户照片 (`POST /api/v1/detections/{detectionId}/photos`)
9. 上传3D模型数据 (`POST /api/v1/detections/{detectionId}/3d-models`)
10. 生成用户报告 (`GET /api/v1/detections/{detectionId}/reports`)

## API迁移指南

### 版本统一改进

#### 1. 解决的问题
- ✅ **版本一致性**: 所有API统一使用 `/api/v1/` 前缀
- ✅ **RESTful规范**: 采用标准的资源路径设计
- ✅ **功能去重**: 合并重复功能的API接口
- ✅ **认证统一**: 统一认证方式和请求头格式

#### 2. 迁移映射表

| 旧API | 新API | 变更说明 |
|-------|-------|----------|
| `POST /api/heartbeat` | `POST /api/v1/devices/{deviceId}/heartbeat` | 添加版本号，使用RESTful路径 |
| `GET /api/device/config` | `GET /api/v1/devices/{deviceId}/config` | 添加版本号，设备ID移至路径 |
| `GET /api/device/status` | `GET /api/v1/devices/{deviceId}/status` | 添加版本号，设备ID移至路径 |
| `POST /api/device/self-check` | `POST /api/v1/devices/{deviceId}/self-check` | 添加版本号，使用RESTful路径 |
| `POST /api/qrcode/generate` | `POST /api/v1/devices/{deviceId}/qrcode` | 添加版本号，关联到设备资源 |
| `POST /api/sms/send` | `POST /api/v1/sms/verification-codes` | 添加版本号，使用复数资源名 |
| `POST /api/user/bind` | `POST /api/v1/users/bind-device` | 添加版本号，使用动作型路径 |
| `POST /api/user/info` | `POST /api/v1/users/{userId}/profile` | 添加版本号，使用RESTful路径 |
| `POST /api/detection/metrics` | `POST /api/v1/detections/{detectionId}/metrics` | 添加版本号，关联到检测记录 |

#### 3. 兼容性策略
- **过渡期**: 新旧API并行运行6个月
- **废弃通知**: 旧API返回 `X-API-Deprecated: true` 头部
- **迁移工具**: 提供自动化迁移脚本
- **文档更新**: 更新所有相关技术文档

#### 4. 实施建议
1. **阶段1**: 部署新版本API，保持旧API正常运行
2. **阶段2**: 更新客户端代码，逐步切换到新API
3. **阶段3**: 监控旧API使用情况，确保迁移完成
4. **阶段4**: 下线旧API，完成版本统一

## 相关文档

- [日常启动云平台API](日常启动/云平台API.md)
- [初始化云平台API接口列表](初始化/云平台API接口列表.md)
- [日常启动流程图](日常启动/流程图.md)
- [初始化流程图](初始化/流程图.md)

---

*最后更新时间: 2024-01-15*