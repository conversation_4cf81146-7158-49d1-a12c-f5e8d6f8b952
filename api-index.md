# 3D体态仪云平台API接口索引

## 概述

本文档整理了3D体态仪设备与云平台交互的所有API接口，包括初始化流程、日常启动流程和日常检测流程中使用的接口。

## API接口列表

### 设备管理类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `GET /api/v1/device/check/{deviceId}` | 检查设备ID是否存在 | 初始化 | 验证设备是否已注册 |
| `POST /api/v1/device/register` | 设备注册/更新 | 初始化 | 注册新设备或更新设备信息 |
| `GET /api/device/status` | 设备状态检查接口 | 日常启动 | 检查设备授权状态和有效期 |
| `GET /api/v1/device/{deviceId}/status` | 设备状态查询 | 初始化 | 查询设备当前状态 |

### 配置管理类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `GET /api/device/config` | 设备配置获取接口 | 日常启动 | 获取设备配置、错误码等信息 |
| `GET /api/v1/device/{deviceId}/config` | 获取设备配置 | 初始化 | 获取设备完整配置信息 |
| `PUT /api/v1/device/{deviceId}/config` | 更新设备配置 | 初始化 | 更新设备配置信息 |
| `GET /api/v1/error-codes` | 获取错误代码配置 | 初始化 | 获取所有错误代码定义 |

### 状态监控类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `POST /api/heartbeat` | 设备心跳接口 | 日常启动 | 设备向云平台发送心跳 |
| `POST /api/v1/device/{deviceId}/heartbeat` | 设备心跳 | 初始化 | 设备定期发送状态信息 |
| `POST /api/device/self-check` | 设备自检结果上报接口 | 日常启动 | 上报设备自检结果 |
| `POST /api/v1/device/{deviceId}/error` | 错误日志上报 | 初始化 | 设备上报错误信息 |

### 用户管理类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `POST /api/qrcode/generate` | 生成二维码 | 日常检测 | 根据设备ID生成二维码 |
| `GET /api/device/bind` | 获取绑定页面 | 日常检测 | 通过二维码获取设备绑定页面 |
| `POST /api/sms/send` | 发送验证码 | 日常检测 | 向用户手机发送验证码 |
| `POST /api/user/bind` | 用户设备绑定 | 日常检测 | 验证码校验并绑定用户与设备 |
| `POST /api/user/info` | 保存用户信息 | 日常检测 | 保存用户个人基本信息 |

### 检测数据类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `POST /api/detection/metrics` | 上传体态检测数据 | 日常检测 | 上传身高、体重、BMI等检测数据 |
| `POST /api/detection/photos` | 上传用户照片 | 日常检测 | 上传用户检测照片 |
| `POST /api/detection/3dmodel` | 上传3D模型数据 | 日常检测 | 上传预处理后的3D模型数据 |
| `GET /api/report/generate` | 生成用户报告 | 日常检测 | 生成并获取用户体态检测报告 |

### 系统管理类API

| API URL | API Title | 来源文档 | 说明 |
|---------|-----------|----------|------|
| `GET /api/v1/health` | 网络连接检查 | 初始化 | 检查云平台服务可用性 |
| `GET /api/v1/version` | 版本管理 | 初始化 | 获取当前API版本信息 |

## 基础信息

### 统一API规范
- **基础URL**: `https://api.cloudplatform.com/v1`
- **认证方式**: API Key + DeviceID
- **数据格式**: JSON
- **字符编码**: UTF-8
- **协议**: HTTPS

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "error": {
    "type": "validation_error",
    "details": "具体错误详情"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 业务流程分类

### 1. 设备初始化流程
设备首次启动或重置后的初始化过程：
1. 检查设备ID是否存在 (`GET /api/v1/device/check/{deviceId}`)
2. 设备注册/更新 (`POST /api/v1/device/register`)
3. 获取设备配置 (`GET /api/v1/device/{deviceId}/config`)
4. 网络连接检查 (`GET /api/v1/health`)
5. 获取错误代码配置 (`GET /api/v1/error-codes`)

### 2. 日常启动流程
设备每日启动时的标准流程：
1. 设备心跳接口 (`POST /api/heartbeat`)
2. 设备配置获取 (`GET /api/device/config`)
3. 设备状态检查 (`GET /api/device/status`)
4. 设备自检结果上报 (`POST /api/device/self-check`)

### 3. 日常检测流程
用户进行体态检测时的完整流程：
1. 生成二维码 (`POST /api/qrcode/generate`)
2. 用户扫码绑定设备 (`GET /api/device/bind`)
3. 发送验证码 (`POST /api/sms/send`)
4. 用户设备绑定 (`POST /api/user/bind`)
5. 保存用户信息 (`POST /api/user/info`)
6. 上传检测数据 (`POST /api/detection/metrics`)
7. 上传用户照片 (`POST /api/detection/photos`)
8. 上传3D模型数据 (`POST /api/detection/3dmodel`)
9. 生成用户报告 (`GET /api/report/generate`)

## 注意事项

1. **API版本差异**: 两套API使用不同的URL格式，建议统一为版本化API
2. **功能重叠**: 部分接口功能相似但实现不同，需要确认使用场景
3. **认证方式**: 两套API的认证方式略有差异，需要统一标准
4. **错误处理**: 建议采用统一的错误码和响应格式

## 相关文档

- [日常启动云平台API](日常启动/云平台API.md)
- [初始化云平台API接口列表](初始化/云平台API接口列表.md)
- [日常启动流程图](日常启动/流程图.md)
- [初始化流程图](初始化/流程图.md)

---

*最后更新时间: 2024-01-15*