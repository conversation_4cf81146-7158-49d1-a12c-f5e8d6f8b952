# 3D体态仪设备日常启动流程图

## 流程图

```plantuml
@startuml
!theme plain
skinparam backgroundColor #FFFFFF
skinparam activity {
  BackgroundColor #F0F8FF
  BorderColor #4682B4
  FontColor #000000
}
skinparam activityDiamond {
  BackgroundColor #FFE4E1
  BorderColor #FF6347
  FontColor #000000
}
skinparam note {
  BackgroundColor #FFFACD
  BorderColor #DAA520
}

start
:3D体态仪设备启动;
:获取本机DeviceID;
:提交心跳到云平台;

if (心跳提交成功?) then (是)
  :根据DeviceID请求配置文件;
  :解析配置文件;
  :设备自检开始;
  :转盘自检;
  :摄像头自检;
  :网络自检;
  :设备运行状态自检;
  :设备有效期自检;
  
  if (设备是否在有效期内?) then (是)
    :启动完成;
    stop
  else (否)
    if (是否有强制下线flag?) then (否)
      :显示提醒弹窗;
      :启动完成;
      stop
    else (是)
      :进入警告页面;
      :设备下线;
      stop
    endif
  endif
else (否)
  :重试计数器+1;
  if (重试次数<10?) then (是)
    :等待5分钟;
    :提交心跳到云平台;
  else (否)
    :弹窗提示网络问题;
    :结束流程;
    stop
  endif
endif

note right
  <b>关键决策点：</b>
  1. 心跳重试机制：确保设备与云平台的连接稳定性
  2. 网络异常处理：提供用户友好的错误提示
  3. 设备授权验证：保护设备使用的合法性
  4. 强制下线机制：支持远程设备管理
end note

@enduml
```

## 流程说明

### 1. 设备启动阶段
- 3D体态仪设备启动
- 获取本机DeviceID

### 2. 心跳机制
- 提交心跳到云平台
- 失败重试机制：最多10次，每次间隔5分钟
- 网络问题处理：弹窗提示用户

### 3. 配置获取
- 根据DeviceID向云平台请求配置文件
- 配置文件包含：
  - 设备配置信息（自动关机时间、转台转速、时区）
  - 设备错误代码、错误消息
  - 系统资源监控（CPU使用率、内存使用率、磁盘使用率、温度）

### 4. 设备自检
按顺序执行以下自检项目：
1. **转盘自检** - 检查转盘功能是否正常
2. **摄像头自检** - 检查摄像头设备状态
3. **网络自检** - 验证网络连接状态
4. **设备运行状态自检** - 检查设备整体运行状态
5. **设备有效期自检** - 验证设备授权状态

### 5. 异常处理
- **设备超期**：显示提醒弹窗
- **强制下线**：进入警告页面，设备下线

## 关键决策点

1. **心跳重试机制**：确保设备与云平台的连接稳定性
2. **网络异常处理**：提供用户友好的错误提示
3. **设备授权验证**：保护设备使用的合法性
4. **强制下线机制**：支持远程设备管理

## 成功路径
设备启动 → 心跳成功 → 配置获取 → 自检通过 → 启动完成

## 异常路径
- 心跳失败 → 重试机制 → 网络提示
- 设备超期 → 提醒弹窗
- 强制下线 → 警告页面 → 设备下线
