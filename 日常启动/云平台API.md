# 3D体态仪设备云平台API接口文档

## 概述

本文档定义了3D体态仪设备日常启动过程中与云平台交互所需的API接口。

## 基础信息

- **基础URL**: `https://api.cloudplatform.com/v1`
- **认证方式**: API Key + DeviceID
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "error": {
    "type": "validation_error",
    "details": "具体错误详情"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## API接口列表

### 1. 设备心跳接口

**接口描述**: 设备向云平台发送心跳，确认设备在线状态

**请求信息**:
- **URL**: `POST /api/heartbeat`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "deviceId": "string",           // 设备唯一标识
  "timestamp": "string",          // 心跳时间戳 (ISO 8601格式)
  "version": "string",            // 设备固件版本
  "status": "string"              // 设备状态 (online/offline/maintenance)
}
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "heartbeatId": "string",      // 心跳记录ID
    "serverTime": "string",       // 服务器时间
    "nextHeartbeat": 300          // 下次心跳间隔(秒)
  }
}
```

**错误码**:
- `400`: 请求参数错误
- `401`: 设备未授权
- `500`: 服务器内部错误

---

### 2. 设备配置获取接口

**接口描述**: 根据设备ID获取设备配置信息

**请求信息**:
- **URL**: `GET /api/device/config`
- **Content-Type**: `application/json`

**请求参数**:
```
Query Parameters:
- deviceId: string (required) - 设备唯一标识
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "deviceConfig": {
      "autoShutdownTime": "22:00",        // 自动关机时间
      "turntableSpeed": 30,               // 转台转速 (rpm)
      "timezone": "Asia/Shanghai",        // 时区设置
      "scanResolution": "high",           // 扫描分辨率
      "dataRetentionDays": 30             // 数据保留天数
    },
    "errorCodes": {
      "E001": "转盘故障",
      "E002": "摄像头故障",
      "E003": "网络连接异常",
      "E004": "存储空间不足",
      "E005": "温度过高"
    },
    "systemMetrics": {
      "cpuUsage": 45,                     // CPU使用率 (%)
      "memoryUsage": 60,                  // 内存使用率 (%)
      "diskUsage": 30,                    // 磁盘使用率 (%)
      "temperature": 35,                  // 设备温度 (°C)
      "uptime": 86400                     // 运行时间 (秒)
    },
    "updateInfo": {
      "hasUpdate": false,                 // 是否有固件更新
      "updateVersion": "1.2.3",           // 更新版本号
      "updateUrl": "https://...",         // 更新下载地址
      "forceUpdate": false                // 是否强制更新
    }
  }
}
```

**错误码**:
- `400`: 设备ID无效
- `404`: 设备配置不存在
- `500`: 服务器内部错误

---

### 3. 设备状态检查接口

**接口描述**: 检查设备授权状态和有效期

**请求信息**:
- **URL**: `GET /api/device/status`
- **Content-Type**: `application/json`

**请求参数**:
```
Query Parameters:
- deviceId: string (required) - 设备唯一标识
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "deviceId": "string",                 // 设备ID
    "isValid": true,                      // 设备是否有效
    "expireDate": "2024-12-31",           // 设备到期日期
    "daysRemaining": 365,                 // 剩余天数
    "forceOffline": false,                // 是否强制下线
    "offlineReason": "",                  // 下线原因
    "licenseType": "standard",            // 许可证类型
    "features": [                         // 可用功能列表
      "3d_scanning",
      "data_export",
      "cloud_sync"
    ],
    "restrictions": {                     // 使用限制
      "maxScansPerDay": 100,              // 每日最大扫描次数
      "maxDataSize": "10GB",              // 最大数据存储量
      "allowedUsers": 5                   // 允许用户数
    }
  }
}
```

**错误码**:
- `400`: 设备ID无效
- `404`: 设备不存在
- `500`: 服务器内部错误

---

### 4. 设备自检结果上报接口

**接口描述**: 设备自检完成后上报自检结果

**请求信息**:
- **URL**: `POST /api/device/self-check`
- **Content-Type**: `application/json`

**请求参数**:
```json
{
  "deviceId": "string",                   // 设备ID
  "checkTime": "string",                  // 自检时间
  "results": {
    "turntable": {                        // 转盘自检结果
      "status": "pass",                   // 状态 (pass/fail/warning)
      "details": "转盘运行正常",
      "errorCode": null
    },
    "camera": {                           // 摄像头自检结果
      "status": "pass",
      "details": "摄像头连接正常",
      "errorCode": null
    },
    "network": {                          // 网络自检结果
      "status": "pass",
      "details": "网络连接正常",
      "errorCode": null,
      "latency": 50                       // 网络延迟(ms)
    },
    "system": {                           // 系统状态自检结果
      "status": "pass",
      "details": "系统运行正常",
      "errorCode": null,
      "metrics": {
        "cpuUsage": 45,
        "memoryUsage": 60,
        "diskUsage": 30,
        "temperature": 35
      }
    }
  }
}
```

**响应参数**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "checkId": "string",                  // 自检记录ID
    "recommendations": [                  // 建议操作
      "建议定期清理设备灰尘",
      "建议更新固件到最新版本"
    ]
  }
}
```

**错误码**:
- `400`: 请求参数错误
- `500`: 服务器内部错误

---

## 错误处理

### 网络异常处理
- 设备应实现重试机制，最多重试10次
- 每次重试间隔5分钟
- 超过重试次数后，向用户显示网络问题提示

### 常见错误码
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 设备未授权 | 检查设备ID和API Key |
| 403 | 设备被禁用 | 联系管理员 |
| 404 | 资源不存在 | 检查设备ID是否正确 |
| 429 | 请求频率过高 | 降低请求频率 |
| 500 | 服务器内部错误 | 稍后重试 |
| 503 | 服务不可用 | 稍后重试 |

## 安全要求

1. **HTTPS**: 所有API调用必须使用HTTPS协议
2. **API Key**: 每个设备需要唯一的API Key进行认证
3. **请求签名**: 重要接口需要请求签名验证
4. **频率限制**: 防止恶意请求，限制请求频率
5. **数据加密**: 敏感数据传输需要加密

## 性能要求

- **响应时间**: 所有接口响应时间 < 3秒
- **可用性**: 99.9%的服务可用性
- **并发支持**: 支持1000+设备同时在线
- **数据一致性**: 确保数据的一致性和完整性

## 版本管理

- **当前版本**: v1.0
- **版本兼容**: 向后兼容至少2个版本
- **版本升级**: 通过配置文件中的updateInfo字段通知设备升级

## 监控和日志

- **请求日志**: 记录所有API请求和响应
- **性能监控**: 监控接口响应时间和成功率
- **异常告警**: 异常情况及时告警
- **数据分析**: 定期分析设备使用情况
