# 3D体态仪设备日常启动时序图

## 时序图

```mermaid
sequenceDiagram
    participant Device as 3D体态仪设备
    participant Local as 本地系统
    participant Cloud as 云平台
    participant User as 用户界面

    Note over Device,User: 设备启动阶段
    Device->>Local: 设备启动
    Local->>Local: 获取本机DeviceID
    
    Note over Device,User: 心跳机制阶段
    loop 心跳重试机制 (最多10次)
        Device->>Cloud: POST /api/heartbeat<br/>{deviceId, timestamp}
        alt 心跳成功
            Cloud-->>Device: 200 OK<br/>{status: "success"}
        else 心跳失败
            Cloud-->>Device: 4xx/5xx Error<br/>{error: "network_error"}
            Device->>Device: 重试计数器+1
            alt 重试次数 < 10
                Device->>Device: 等待5分钟
            else 重试次数 >= 10
                Device->>User: 弹窗提示网络问题
                Note over Device,User: 流程结束
            end
        end
    end
    
    Note over Device,User: 配置获取阶段
    Device->>Cloud: GET /api/device/config<br/>?deviceId={deviceId}
    Cloud-->>Device: 200 OK<br/>{<br/>  deviceConfig: {<br/>    autoShutdownTime: "22:00",<br/>    turntableSpeed: 30,<br/>    timezone: "Asia/Shanghai"<br/>  },<br/>  errorCodes: {...},<br/>  systemMetrics: {<br/>    cpuUsage: 45,<br/>    memoryUsage: 60,<br/>    diskUsage: 30,<br/>    temperature: 35<br/>  }<br/>}
    
    Note over Device,User: 设备自检阶段
    Device->>Device: 转盘自检
    Device->>Device: 摄像头自检
    Device->>Device: 网络自检
    Device->>Device: 设备运行状态自检
    
    Note over Device,User: 设备有效期检查
    Device->>Cloud: GET /api/device/status<br/>?deviceId={deviceId}
    Cloud-->>Device: 200 OK<br/>{<br/>  isValid: true/false,<br/>  expireDate: "2024-12-31",<br/>  forceOffline: false<br/>}
    
    alt 设备在有效期内
        Device->>User: 启动完成
    else 设备超过有效期
        alt 无强制下线flag
            Device->>User: 显示提醒弹窗
            User->>Device: 用户确认
            Device->>User: 启动完成
        else 有强制下线flag
            Device->>User: 进入警告页面
            Note over Device,User: 设备下线
        end
    end
```

## 时序说明

### 阶段1：设备启动
- **参与者**：3D体态仪设备、本地系统
- **操作**：设备启动，获取本机DeviceID
- **时间**：设备启动后立即执行

### 阶段2：心跳机制
- **参与者**：3D体态仪设备、云平台
- **操作**：向云平台发送心跳请求
- **重试机制**：
  - 失败时最多重试10次
  - 每次重试间隔5分钟
  - 超过重试次数后弹窗提示用户

### 阶段3：配置获取
- **参与者**：3D体态仪设备、云平台
- **操作**：根据DeviceID请求设备配置文件
- **返回数据**：
  - 设备配置信息（自动关机时间、转台转速、时区）
  - 错误代码和消息
  - 系统资源监控数据

### 阶段4：设备自检
- **参与者**：3D体态仪设备
- **操作**：按顺序执行各项自检
- **自检项目**：
  1. 转盘自检
  2. 摄像头自检
  3. 网络自检
  4. 设备运行状态自检

### 阶段5：设备有效期检查
- **参与者**：3D体态仪设备、云平台、用户界面
- **操作**：检查设备授权状态
- **处理逻辑**：
  - 设备在有效期内：正常启动完成
  - 设备超期但无强制下线：显示提醒弹窗
  - 设备超期且有强制下线：进入警告页面并下线

## 关键时间点

1. **T0**：设备启动
2. **T1**：获取DeviceID（< 1秒）
3. **T2**：首次心跳请求（< 5秒）
4. **T3**：配置获取（< 10秒）
5. **T4**：设备自检完成（< 30秒）
6. **T5**：有效期检查（< 5秒）
7. **T6**：启动完成（总计 < 60秒）

## 异常处理时序

### 网络异常
```
设备 -> 云平台：心跳请求
云平台 -> 设备：网络错误
设备 -> 设备：等待5分钟
设备 -> 云平台：重试心跳请求
...（最多10次）
设备 -> 用户：网络问题弹窗
```

### 设备超期
```
设备 -> 云平台：状态检查
云平台 -> 设备：设备超期信息
设备 -> 用户：提醒弹窗
用户 -> 设备：确认操作
设备 -> 用户：启动完成
```

### 强制下线
```
设备 -> 云平台：状态检查
云平台 -> 设备：强制下线标志
设备 -> 用户：警告页面
设备 -> 设备：执行下线
```

## 性能要求

- **心跳响应时间**：< 3秒
- **配置获取时间**：< 5秒
- **自检完成时间**：< 30秒
- **总启动时间**：< 60秒
- **重试间隔**：5分钟
- **最大重试次数**：10次
