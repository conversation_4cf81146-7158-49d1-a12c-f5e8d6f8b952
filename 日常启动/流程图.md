# 3D体态仪设备日常启动流程图

## 流程图

```mermaid
flowchart TD
    A[3D体态仪设备启动] --> B[获取本机DeviceID]
    B --> C[提交心跳到云平台]
    C --> D{心跳提交成功?}
    D -->|是| E[根据DeviceID请求配置文件]
    D -->|否| F[重试计数器+1]
    F --> G{重试次数<10?}
    G -->|是| H[等待5分钟]
    H --> C
    G -->|否| I[弹窗提示网络问题]
    I --> J[结束流程]
    
    E --> K[解析配置文件]
    K --> L[设备自检开始]
    L --> M[转盘自检]
    M --> N[摄像头自检]
    N --> O[网络自检]
    O --> P[设备运行状态自检]
    P --> Q[设备有效期自检]
    Q --> R{设备是否在有效期内?}
    R -->|是| S[启动完成]
    R -->|否| T{是否有强制下线flag?}
    T -->|否| U[显示提醒弹窗]
    T -->|是| V[进入警告页面]
    U --> S
    V --> W[设备下线]
```

## 流程说明

### 1. 设备启动阶段
- 3D体态仪设备启动
- 获取本机DeviceID

### 2. 心跳机制
- 提交心跳到云平台
- 失败重试机制：最多10次，每次间隔5分钟
- 网络问题处理：弹窗提示用户

### 3. 配置获取
- 根据DeviceID向云平台请求配置文件
- 配置文件包含：
  - 设备配置信息（自动关机时间、转台转速、时区）
  - 设备错误代码、错误消息
  - 系统资源监控（CPU使用率、内存使用率、磁盘使用率、温度）

### 4. 设备自检
按顺序执行以下自检项目：
1. **转盘自检** - 检查转盘功能是否正常
2. **摄像头自检** - 检查摄像头设备状态
3. **网络自检** - 验证网络连接状态
4. **设备运行状态自检** - 检查设备整体运行状态
5. **设备有效期自检** - 验证设备授权状态

### 5. 异常处理
- **设备超期**：显示提醒弹窗
- **强制下线**：进入警告页面，设备下线

## 关键决策点

1. **心跳重试机制**：确保设备与云平台的连接稳定性
2. **网络异常处理**：提供用户友好的错误提示
3. **设备授权验证**：保护设备使用的合法性
4. **强制下线机制**：支持远程设备管理

## 成功路径
设备启动 → 心跳成功 → 配置获取 → 自检通过 → 启动完成

## 异常路径
- 心跳失败 → 重试机制 → 网络提示
- 设备超期 → 提醒弹窗
- 强制下线 → 警告页面 → 设备下线
