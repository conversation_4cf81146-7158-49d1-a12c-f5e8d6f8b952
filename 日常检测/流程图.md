# 3D体态仪设备日常检测流程图

## 流程图

```mermaid
flowchart TD
    A[3D体态仪设备启动] --> B[设备自检完成]
    B --> C[进入扫描二维码页面]
    C --> D[向云平台提交DeviceID获取二维码]
    D --> E[云平台生成二维码图片]
    E --> F[设备展示二维码给用户]
    F --> G[用户扫描二维码]
    G --> H[用户进入绑定页面]
    H --> I[用户输入手机号码]
    I --> J[云平台发送验证码]
    J --> K[用户输入验证码]
    K --> L{验证码是否正确?}
    L -->|否| J
    L -->|是| M[云平台绑定用户信息和DeviceID]
    M --> N[返回Wechat_ID给设备]
    N --> O[进入用户个人信息输入页面]
    O --> P[用户输入姓名、身高、年龄]
    P --> Q[设备提交用户信息到云平台]
    Q --> R[设备开始检测用户状态]
    R --> S[采集身高、体重、BMI数据]
    S --> T[提交检测数据到云平台]
    T --> U[设备开始采集用户照片]
    U --> V[提交照片到云平台]
    V --> W[设备开始预处理3D模型]
    W --> X[提交3D模型到云平台]
    X --> Y[设备获取云平台用户报告]
    Y --> Z[设备展示用户报告]
    Z --> END[检测流程完成]
```

## 流程说明

### 1. 设备准备阶段
- 3D体态仪设备启动
- 完成设备自检

### 2. 用户绑定阶段
- 设备生成并展示二维码
- 用户扫码进入绑定流程
- 手机号验证和用户绑定

### 3. 信息采集阶段
- 用户输入个人基本信息
- 设备采集体态数据
- 设备拍摄用户照片

### 4. 数据处理阶段
- 3D模型预处理
- 数据上传到云平台
- 生成用户报告

### 5. 结果展示阶段
- 获取并展示用户报告
- 完成检测流程

## 关键决策点

1. **验证码验证**：确保用户身份的真实性
2. **数据采集顺序**：按照身高体重→照片→3D模型的顺序进行
3. **云平台交互**：每个阶段都需要与云平台进行数据交互
4. **用户体验**：流程设计注重用户操作的便捷性