# 3D体态仪设备日常检测时序图

## 时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Device as 3D体态仪设备
    participant Cloud as 云平台
    participant WeChat as 微信小程序

    Note over User,WeChat: 设备准备阶段
    Device->>Device: 设备启动
    Device->>Device: 设备自检完成
    
    Note over User,WeChat: 二维码生成阶段
    Device->>Cloud: POST /api/qrcode/generate<br/>{deviceId}
    Cloud->>Cloud: 生成二维码图片
    Cloud-->>Device: 200 OK<br/>{qrCodeUrl, qrCodeImage}
    Device->>User: 展示二维码
    
    Note over User,WeChat: 用户绑定阶段
    User->>WeChat: 扫描二维码
    WeChat->>Cloud: GET /api/device/bind<br/>?qrCode={qrCode}
    Cloud-->>WeChat: 绑定页面
    User->>WeChat: 输入手机号码
    WeChat->>Cloud: POST /api/sms/send<br/>{phone}
    Cloud-->>WeChat: 验证码发送成功
    User->>WeChat: 输入验证码
    WeChat->>Cloud: POST /api/user/bind<br/>{phone, code, deviceId}
    Cloud->>Cloud: 验证码校验
    Cloud->>Cloud: 绑定用户和设备
    Cloud-->>WeChat: 绑定成功<br/>{wechatId, userId}
    Cloud->>Device: 推送绑定结果<br/>{wechatId, userId}
    
    Note over User,WeChat: 信息采集阶段
    Device->>User: 显示个人信息输入页面
    User->>Device: 输入姓名、身高、年龄
    Device->>Cloud: POST /api/user/info<br/>{userId, name, height, age}
    Cloud-->>Device: 信息保存成功
    
    Note over User,WeChat: 体态检测阶段
    Device->>Device: 开始检测用户状态
    Device->>Device: 采集身高、体重、BMI
    Device->>Cloud: POST /api/detection/metrics<br/>{userId, height, weight, bmi}
    Cloud-->>Device: 数据保存成功
    
    Note over User,WeChat: 照片采集阶段
    Device->>Device: 开始采集用户照片
    Device->>Cloud: POST /api/detection/photos<br/>{userId, photos[]}
    Cloud-->>Device: 照片上传成功
    
    Note over User,WeChat: 3D模型处理阶段
    Device->>Device: 开始预处理3D模型
    Device->>Cloud: POST /api/detection/3dmodel<br/>{userId, modelData}
    Cloud-->>Device: 3D模型上传成功
    Cloud->>Cloud: 后台处理3D模型
    
    Note over User,WeChat: 报告生成阶段
    Device->>Cloud: GET /api/report/generate<br/>?userId={userId}
    Cloud->>Cloud: 生成用户报告
    Cloud-->>Device: 200 OK<br/>{reportData}
    Device->>User: 展示用户报告
```

## 时序说明

### 阶段1：设备准备
- **参与者**：3D体态仪设备
- **操作**：设备启动和自检
- **时间**：< 60秒

### 阶段2：二维码生成
- **参与者**：设备、云平台
- **操作**：生成并展示二维码
- **时间**：< 5秒

### 阶段3：用户绑定
- **参与者**：用户、微信小程序、云平台、设备
- **操作**：扫码、验证、绑定
- **时间**：2-5分钟

### 阶段4：信息采集
- **参与者**：用户、设备、云平台
- **操作**：输入个人信息
- **时间**：1-2分钟

### 阶段5：体态检测
- **参与者**：设备、云平台
- **操作**：采集体态数据
- **时间**：30-60秒

### 阶段6：照片采集
- **参与者**：设备、云平台
- **操作**：拍摄并上传照片
- **时间**：30-60秒

### 阶段7：3D模型处理
- **参与者**：设备、云平台
- **操作**：3D模型预处理和上传
- **时间**：2-5分钟

### 阶段8：报告生成
- **参与者**：设备、云平台、用户
- **操作**：生成并展示报告
- **时间**：30-60秒

## 性能要求

- **二维码生成**：< 3秒
- **用户绑定**：< 10秒
- **数据上传**：< 5秒
- **报告生成**：< 30秒
- **总检测时间**：< 10分钟