# 3D体态仪设备日常检测云平台API接口文档

## 概述

本文档定义了3D体态仪设备日常检测过程中与云平台交互所需的API接口。

## 基础信息

- **基础URL**: `https://api.cloudplatform.com/v1`
- **认证方式**: API Key + DeviceID
- **数据格式**: JSON
- **字符编码**: UTF-8

## 1. 二维码管理API

### 1.1 生成二维码
- **接口**: `POST /api/qrcode/generate`
- **功能**: 根据设备ID生成二维码图片

**请求参数**:
```json
{
  "deviceId": "string"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "qrCode": "QR_CODE_STRING",
    "qrCodeUrl": "https://api.cloudplatform.com/qr/abc123",
    "qrCodeImage": "base64_encoded_image",
    "expireTime": "2024-01-01T13:00:00Z"
  }
}
```

## 2. 用户绑定API

### 2.1 获取绑定页面
- **接口**: `GET /api/device/bind`
- **功能**: 通过二维码获取设备绑定页面

**请求参数**:
- `qrCode` (query): 二维码字符串

**响应**: 返回HTML绑定页面

### 2.2 发送验证码
- **接口**: `POST /api/sms/send`
- **功能**: 向用户手机发送验证码

**请求参数**:
```json
{
  "phone": "string",
  "deviceId": "string"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "smsId": "SMS_123456",
    "expireTime": "2024-01-01T12:05:00Z"
  }
}
```

### 2.3 用户设备绑定
- **接口**: `POST /api/user/bind`
- **功能**: 验证码校验并绑定用户与设备

**请求参数**:
```json
{
  "phone": "string",
  "verificationCode": "string",
  "deviceId": "string",
  "smsId": "string"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "绑定成功",
  "data": {
    "userId": "USER_123456",
    "wechatId": "WECHAT_789012",
    "bindTime": "2024-01-01T12:00:00Z"
  }
}
```

## 3. 用户信息API

### 3.1 保存用户信息
- **接口**: `POST /api/user/info`
- **功能**: 保存用户个人基本信息

**请求参数**:
```json
{
  "userId": "string",
  "name": "string",
  "height": 175.5,
  "age": 25,
  "gender": "male"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "用户信息保存成功",
  "data": {
    "userId": "USER_123456",
    "updateTime": "2024-01-01T12:00:00Z"
  }
}
```

## 4. 检测数据API

### 4.1 上传体态检测数据
- **接口**: `POST /api/detection/metrics`
- **功能**: 上传身高、体重、BMI等检测数据

**请求参数**:
```json
{
  "userId": "string",
  "deviceId": "string",
  "detectionTime": "2024-01-01T12:00:00Z",
  "metrics": {
    "height": 175.5,
    "weight": 70.2,
    "bmi": 22.8,
    "bodyFat": 15.5,
    "muscleMass": 32.1
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "检测数据保存成功",
  "data": {
    "detectionId": "DETECTION_123456",
    "saveTime": "2024-01-01T12:00:00Z"
  }
}
```

### 4.2 上传用户照片
- **接口**: `POST /api/detection/photos`
- **功能**: 上传用户检测照片

**请求参数**:
```json
{
  "userId": "string",
  "deviceId": "string",
  "detectionId": "string",
  "photos": [
    {
      "type": "front",
      "image": "base64_encoded_image",
      "timestamp": "2024-01-01T12:00:00Z"
    },
    {
      "type": "side",
      "image": "base64_encoded_image", 
      "timestamp": "2024-01-01T12:00:01Z"
    }
  ]
}
```

**响应**:
```json
{
  "code": 200,
  "message": "照片上传成功",
  "data": {
    "photoIds": ["PHOTO_001", "PHOTO_002"],
    "uploadTime": "2024-01-01T12:00:00Z"
  }
}
```

### 4.3 上传3D模型数据
- **接口**: `POST /api/detection/3dmodel`
- **功能**: 上传预处理后的3D模型数据

**请求参数**:
```json
{
  "userId": "string",
  "deviceId": "string", 
  "detectionId": "string",
  "modelData": {
    "format": "obj",
    "data": "base64_encoded_model_data",
    "metadata": {
      "vertices": 50000,
      "faces": 100000,
      "processingTime": 120
    }
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "3D模型上传成功",
  "data": {
    "modelId": "MODEL_123456",
    "uploadTime": "2024-01-01T12:00:00Z",
    "processingStatus": "queued"
  }
}
```

## 5. 报告生成API

### 5.1 生成用户报告
- **接口**: `GET /api/report/generate`
- **功能**: 生成并获取用户体态检测报告

**请求参数**:
- `userId` (query): 用户ID
- `detectionId` (query): 检测ID

**响应**:
```json
{
  "code": 200,
  "message": "报告生成成功",
  "data": {
    "reportId": "REPORT_123456",
    "userId": "USER_123456",
    "detectionTime": "2024-01-01T12:00:00Z",
    "summary": {
      "overallScore": 85,
      "healthLevel": "良好",
      "recommendations": ["建议增加运动", "注意饮食均衡"]
    },
    "details": {
      "posture": {
        "score": 80,
        "issues": ["轻微前倾", "肩膀不平衡"]
      },
      "bodyComposition": {
        "bmi": 22.8,
        "bodyFat": 15.5,
        "muscleMass": 32.1
      },
      "measurements": {
        "height": 175.5,
        "weight": 70.2,
        "chestCircumference": 95.0,
        "waistCircumference": 80.0
      }
    },
    "images": {
      "frontView": "https://cdn.cloudplatform.com/reports/front_123456.jpg",
      "sideView": "https://cdn.cloudplatform.com/reports/side_123456.jpg",
      "3dModel": "https://cdn.cloudplatform.com/reports/model_123456.obj"
    }
  }
}
```

## 错误处理

### 常见错误码
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 设备未授权 | 检查API Key |
| 403 | 用户未绑定设备 | 重新进行绑定流程 |
| 404 | 资源不存在 | 检查用户ID或设备ID |
| 413 | 文件过大 | 压缩图片或模型数据 |
| 429 | 请求频率过高 | 降低请求频率 |
| 500 | 服务器内部错误 | 稍后重试 |

### 业务错误码
- `10001`: 二维码已过期
- `10002`: 验证码错误
- `10003`: 手机号已绑定其他设备
- `10004`: 设备已被其他用户绑定
- `10005`: 检测数据格式错误
- `10006`: 3D模型处理失败

## 安全要求

1. **HTTPS**: 所有API调用必须使用HTTPS协议
2. **API Key**: 每个设备需要唯一的API Key
3. **数据加密**: 敏感数据传输需要加密
4. **文件验证**: 上传文件需要格式和大小验证
5. **频率限制**: 防止恶意请求

## 性能要求

- **二维码生成**: < 3秒
- **验证码发送**: < 5秒
- **数据上传**: < 10秒
- **报告生成**: < 30秒
- **文件上传**: 支持最大50MB文件