# 云平台API接口列表

## 概述

根据3D体态仪初始化流程图分析，云平台需要提供以下API接口来支持设备初始化、配置管理和状态监控等功能。

## 1. 设备注册/验证API

### 1.1 检查设备ID是否存在
- **接口**: `GET /api/v1/device/check/{deviceId}`
- **功能**: 检查DeviceID是否已在云平台注册
- **请求参数**:
  - `deviceId` (path): 设备唯一标识符
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "设备检查完成",
    "data": {
      "exists": true,
      "deviceId": "DEVICE_001",
      "status": "active",
      "lastSeen": "2024-01-15T10:30:00Z"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

### 1.2 设备注册/更新
- **接口**: `POST /api/v1/device/register`
- **功能**: 注册新设备或更新现有设备信息
- **请求参数**:
  ```json
  {
    "deviceId": "string",
    "apkVersion": "string", 
    "ipAddress": "string",
    "turntableStatus": "string",
    "cameraStatus": "string",
    "hardwareFeatures": {
      "cpu": "string",
      "memory": "string",
      "storage": "string",
      "network": "string"
    }
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "设备注册成功",
    "data": {
      "deviceId": "DEVICE_001",
      "registrationTime": "2024-01-15T10:30:00Z",
      "status": "registered"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

## 2. 配置管理API

### 2.1 获取设备配置
- **接口**: `GET /api/v1/device/{deviceId}/config`
- **功能**: 获取设备的完整配置信息
- **请求参数**:
  - `deviceId` (path): 设备唯一标识符
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "配置获取成功",
    "data": {
      "deviceConfig": {
        "autoShutdownTime": "23:00",
        "turntableSpeed": 30,
        "timezone": "Asia/Shanghai",
        "language": "zh-CN"
      },
      "errorCodeConfig": {
        "errorCodes": [
          {
            "code": "E001",
            "message": "CPU使用率过高",
            "cpuThreshold": 80,
            "memoryThreshold": 85,
            "diskThreshold": 90,
            "temperatureThreshold": 70
          },
          {
            "code": "E002", 
            "message": "内存不足",
            "cpuThreshold": 90,
            "memoryThreshold": 95,
            "diskThreshold": 85,
            "temperatureThreshold": 75
          }
        ]
      }
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

### 2.2 更新设备配置
- **接口**: `PUT /api/v1/device/{deviceId}/config`
- **功能**: 更新设备配置信息
- **请求参数**:
  - `deviceId` (path): 设备唯一标识符
  - 请求体同获取配置的响应格式
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "配置更新成功",
    "data": {
      "deviceId": "DEVICE_001",
      "updateTime": "2024-01-15T10:30:00Z"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

## 3. 设备状态监控API

### 3.1 设备心跳
- **接口**: `POST /api/v1/device/{deviceId}/heartbeat`
- **功能**: 设备定期发送状态信息
- **请求参数**:
  ```json
  {
    "deviceId": "string",
    "timestamp": "2024-01-15T10:30:00Z",
    "status": "online",
    "cpuUsage": 45.2,
    "memoryUsage": 67.8,
    "diskUsage": 23.5,
    "temperature": 42.1,
    "networkStatus": "connected",
    "batteryLevel": 85
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "心跳接收成功",
    "data": {
      "deviceId": "DEVICE_001",
      "nextHeartbeat": 300,
      "configUpdated": false
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

### 3.2 设备状态查询
- **接口**: `GET /api/v1/device/{deviceId}/status`
- **功能**: 查询设备当前状态
- **请求参数**:
  - `deviceId` (path): 设备唯一标识符
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "状态查询成功",
    "data": {
      "deviceId": "DEVICE_001",
      "status": "online",
      "lastHeartbeat": "2024-01-15T10:30:00Z",
      "uptime": 86400,
      "currentConfig": {
        "autoShutdownTime": "23:00",
        "turntableSpeed": 30
      }
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

## 4. 错误处理API

### 4.1 错误日志上报
- **接口**: `POST /api/v1/device/{deviceId}/error`
- **功能**: 设备上报错误信息
- **请求参数**:
  ```json
  {
    "deviceId": "string",
    "errorCode": "E001",
    "errorMessage": "CPU使用率超过阈值",
    "timestamp": "2024-01-15T10:30:00Z",
    "context": {
      "cpuUsage": 85.5,
      "memoryUsage": 67.2,
      "temperature": 68.3,
      "processes": ["app1", "app2"]
    },
    "severity": "warning"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "错误日志接收成功",
    "data": {
      "errorId": "ERR_20240115_103000_001",
      "acknowledged": true
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

### 4.2 获取错误代码配置
- **接口**: `GET /api/v1/error-codes`
- **功能**: 获取所有错误代码定义
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "错误代码配置获取成功",
    "data": {
      "errorCodes": [
        {
          "code": "E001",
          "name": "CPU使用率过高",
          "description": "CPU使用率超过设定阈值",
          "thresholds": {
            "cpu": 80,
            "memory": 85,
            "disk": 90,
            "temperature": 70
          },
          "severity": "warning",
          "action": "restart_process"
        }
      ]
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

## 5. 系统管理API

### 5.1 网络连接检查
- **接口**: `GET /api/v1/health`
- **功能**: 检查云平台服务可用性
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "服务正常",
    "data": {
      "status": "healthy",
      "version": "1.0.0",
      "uptime": 86400,
      "services": {
        "database": "healthy",
        "cache": "healthy",
        "storage": "healthy"
      }
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

### 5.2 版本管理
- **接口**: `GET /api/v1/version`
- **功能**: 获取当前API版本信息
- **响应**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "版本信息获取成功",
    "data": {
      "apiVersion": "1.0.0",
      "minSupportedVersion": "1.0.0",
      "latestVersion": "1.2.0",
      "deprecatedVersions": ["0.9.x"],
      "releaseNotes": "https://api.example.com/release-notes"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
  ```

## 6. API设计规范

### 6.1 通用响应格式
所有API响应都遵循统一的JSON格式：
```json
{
  "success": "boolean",
  "code": "number",
  "message": "string", 
  "data": "object",
  "timestamp": "datetime"
}
```

### 6.2 HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `429`: 请求频率超限
- `500`: 服务器内部错误
- `503`: 服务不可用

### 6.3 错误码定义
- `200`: 操作成功
- `40001`: 设备ID格式错误
- `40002`: 设备未注册
- `40003`: 配置参数无效
- `50001`: 数据库连接失败
- `50002`: 配置服务异常

### 6.4 安全认证
- 使用HTTPS协议
- API密钥认证 (Header: `X-API-Key`)
- 请求签名验证
- IP白名单控制

### 6.5 限流策略
- 设备注册: 每分钟最多10次
- 心跳上报: 每5分钟1次
- 错误上报: 每分钟最多50次
- 配置查询: 每分钟最多20次

### 6.6 数据格式
- 时间格式: ISO 8601 (`2024-01-15T10:30:00Z`)
- 数字格式: 浮点数保留2位小数
- 字符串编码: UTF-8
- 内容类型: `application/json`

## 7. 部署和监控

### 7.1 环境配置
- 开发环境: `https://dev-api.3dbody.com`
- 测试环境: `https://test-api.3dbody.com`
- 生产环境: `https://api.3dbody.com`

### 7.2 监控指标
- API响应时间
- 请求成功率
- 错误率统计
- 设备在线率
- 配置更新频率

### 7.3 日志记录
- 所有API调用记录
- 错误日志详细记录
- 性能指标监控
- 安全事件审计

## 8. 版本更新策略

### 8.1 版本兼容性
- 向后兼容至少2个主版本
- 废弃API提前6个月通知
- 提供迁移指南和工具

### 8.2 更新通知
- 邮件通知管理员
- API响应头包含版本信息
- 维护窗口提前通知

---

*本文档基于3D体态仪初始化流程图分析生成，涵盖了设备初始化、配置管理、状态监控等核心功能的API接口设计。*
