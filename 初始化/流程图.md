# 3D体态仪设备和云平台初始化流程图

## 流程图

```mermaid
flowchart TD
    A[设备启动] --> B[采集硬件特征]
    B --> C[生成DeviceID]
    C --> D[检查网络连接]
    D --> E{网络是否可用?}
    
    E -->|否| F[提示无网络]
    F --> G[等待5分钟]
    G --> D
    
    E -->|是| H[提交DeviceID到云平台，此处需要首先判断DeviceID是否存在]
    H --> I{提交是否成功?}
    
    I -->|否| J[等待5分钟]
    J --> H
    
    I -->|是| K[请求配置文件]
    K --> L[接收配置文件]
    L --> M[解析设备配置信息]
    M --> N[解析错误代码配置]
    N --> O[应用配置]
    O --> P[初始化完成]
    
    %% 提交信息详情
    H --> H1[提交信息包含:<br/>- DeviceID<br/>- APK Version<br/>- IP地址<br/>- 转盘状态<br/>- 摄像头状态]
    
    %% 配置文件详情
    L --> L1[配置文件包含:<br/>a. 设备配置信息:<br/>   - 自动关机时间<br/>   - 转台转速<br/>   - 时区设置<br/>b. 错误代码配置:<br/>   - 错误代码<br/>   - 错误消息<br/>   - CPU使用率<br/>   - 内存使用率<br/>   - 磁盘使用率<br/>   - 温度监控]
    
  
   
```

## 流程说明

### 1. 设备启动阶段
- 设备启动后开始初始化流程

### 2. 硬件特征采集
- 采集本机硬件特征信息
- 生成唯一的DeviceID

### 3. 网络连接检查
- 检查设备网络连接状态
- 如果无网络，提示用户并等待5分钟后重试

### 4. DeviceID提交
- 向云平台提交设备信息
- 包含：DeviceID、APK版本、IP地址、转盘状态、摄像头状态
- 如果提交失败，每5分钟重试一次

### 5. 配置文件获取
- 成功提交DeviceID后，请求云平台配置文件
- 配置文件包含设备配置和错误代码配置

### 6. 配置应用
- 解析并应用设备配置信息
- 设置错误代码和监控参数
- 完成初始化流程

## 关键特性

- **重试机制**: 网络连接失败或DeviceID提交失败时，每5分钟自动重试
- **错误处理**: 网络不可用时提供用户友好的提示
- **配置管理**: 从云平台动态获取设备配置，支持远程管理
- **状态监控**: 实时监控设备硬件状态和性能指标
