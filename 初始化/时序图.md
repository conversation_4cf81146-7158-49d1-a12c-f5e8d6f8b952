# 3D体态仪设备初始化时序图

## 时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as 3D体态仪设备
    participant N as 网络
    participant C as 云平台

    Note over U,C: 设备初始化流程

    %% 设备启动阶段
    U->>D: 启动设备
    D->>D: 采集硬件特征
    D->>D: 生成DeviceID

    %% 网络检查阶段
    D->>N: 检查网络连接
    alt 网络不可用
        N-->>D: 网络连接失败
        D->>U: 提示无网络
        Note over D: 等待5分钟
        D->>N: 重新检查网络连接
    else 网络可用
        N-->>D: 网络连接成功
    end

    %% DeviceID提交阶段
    loop 直到提交成功
        D->>C: 提交DeviceID和设备信息
        Note right of D: 包含信息:<br/>- DeviceID<br/>- APK Version<br/>- IP地址<br/>- 转盘状态<br/>- 摄像头状态
        
        alt 提交失败
            C-->>D: 提交失败响应
            Note over D: 等待5分钟
        else 提交成功
            C-->>D: 提交成功响应
        end
    end

    %% 配置文件获取阶段
    D->>C: 请求配置文件
    C-->>D: 返回配置文件
    Note right of C: 配置文件包含:<br/>a. 设备配置信息:<br/>   - 自动关机时间<br/>   - 转台转速<br/>   - 时区设置<br/>b. 错误代码配置:<br/>   - 错误代码<br/>   - 错误消息<br/>   - CPU使用率<br/>   - 内存使用率<br/>   - 磁盘使用率<br/>   - 温度监控

    %% 配置应用阶段
    D->>D: 解析设备配置信息
    D->>D: 解析错误代码配置
    D->>D: 应用配置
    D->>U: 初始化完成

    Note over U,C: 初始化流程结束
```

## 时序说明

### 参与者说明
- **用户**: 操作3D体态仪设备的用户
- **3D体态仪设备**: 需要进行初始化的硬件设备
- **网络**: 网络连接环境
- **云平台**: 提供设备管理和配置服务的云端平台

### 主要交互流程

#### 1. 设备启动阶段
- 用户启动设备
- 设备自动采集硬件特征并生成唯一DeviceID

#### 2. 网络连接检查
- 设备检查网络连接状态
- 如果网络不可用，提示用户并等待5分钟后重试
- 如果网络可用，继续后续流程

#### 3. DeviceID提交阶段
- 设备向云平台提交DeviceID和相关信息
- 提交信息包括：DeviceID、APK版本、IP地址、转盘状态、摄像头状态
- 如果提交失败，每5分钟重试一次，直到成功

#### 4. 配置文件获取
- 成功提交DeviceID后，设备请求云平台配置文件
- 云平台返回包含设备配置和错误代码配置的完整配置文件

#### 5. 配置应用阶段
- 设备解析并应用接收到的配置信息
- 设置错误代码和监控参数
- 向用户确认初始化完成

### 关键特性

#### 重试机制
- 网络连接失败时，每5分钟自动重试
- DeviceID提交失败时，每5分钟自动重试
- 确保设备最终能够成功完成初始化

#### 错误处理
- 网络不可用时提供用户友好的提示
- 自动重试机制减少用户干预需求

#### 配置管理
- 从云平台动态获取设备配置
- 支持远程管理和配置更新
- 包含完整的设备参数和监控配置

#### 状态监控
- 实时监控设备硬件状态
- 配置性能指标监控参数
- 支持设备健康状态管理
